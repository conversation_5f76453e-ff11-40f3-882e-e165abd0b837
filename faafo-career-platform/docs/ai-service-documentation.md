# AI Service Documentation

## Overview

The FAAFO Career Platform AI Service is a comprehensive, production-grade system built on Google's Gemini AI with advanced performance optimizations, security hardening, and intelligent monitoring.

## 🎯 Key Features

### Core AI Capabilities
- **Resume Analysis**: Comprehensive resume evaluation with skills identification and improvement suggestions
- **Career Recommendations**: Personalized career path suggestions based on skills and preferences
- **Interview Question Generation**: Dynamic interview questions tailored to roles and experience levels
- **Skills Gap Analysis**: Identification of skill gaps and learning recommendations
- **Interview Response Analysis**: AI-powered feedback on interview performance

### Performance Optimizations
- **Multi-tier Caching**: L1 (memory) + L2 (Redis) + intelligent cache warming
- **Request Optimization**: Batching, deduplication, and intelligent queuing
- **Predictive Performance**: Real-time monitoring with automatic optimization
- **Self-healing Architecture**: Automatic error recovery and fallback mechanisms

### Security Features
- **Input Validation**: Comprehensive sanitization and threat detection
- **Rate Limiting**: User-based request throttling with intelligent scaling
- **Content Filtering**: XSS, SQL injection, and malicious content prevention
- **Security Monitoring**: Real-time threat detection and alerting

## 🏗️ Architecture

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │───▶│  API Gateway    │───▶│  AI Service     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Request         │◀───┤ Performance     │
                       │ Optimizer       │    │ Monitor         │
                       └─────────────────┘    └─────────────────┘
                                │                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Advanced Cache  │    │ Security        │
                       │ Manager         │    │ Validator       │
                       └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │ Redis Cache     │
                       │ + Memory Cache  │
                       └─────────────────┘
```

### Core Classes

1. **GeminiService**: Main AI service interface
2. **AdvancedCacheManager**: Multi-tier intelligent caching
3. **RequestOptimizer**: Request batching and optimization
4. **PerformanceMonitor**: Real-time performance tracking
5. **AIInputValidator**: Security validation and sanitization
6. **AIResponseParser**: Response parsing with fallbacks
7. **AIServiceMonitor**: Service monitoring and analytics

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Redis (optional, falls back to memory cache)
- Google Gemini API key

### Environment Variables
```bash
GOOGLE_GEMINI_API_KEY=your_gemini_api_key
REDIS_URL=redis://localhost:6379  # Optional
```

### Basic Usage

```typescript
import { geminiService } from '@/lib/services/geminiService';

// Resume Analysis
const resumeResult = await geminiService.analyzeResume(
  resumeText, 
  userId
);

// Career Recommendations
const careerResult = await geminiService.generateCareerRecommendations(
  assessmentData,
  currentSkills,
  preferences,
  userId
);

// Interview Questions
const questionsResult = await geminiService.generateInterviewQuestions({
  sessionType: 'TECHNICAL_PRACTICE',
  careerPath: 'Software Engineer',
  experienceLevel: 'SENIOR',
  difficulty: 'INTERMEDIATE',
  count: 5
});
```

## 📊 Performance Monitoring

### Health Check Endpoint
```bash
GET /api/admin/ai-performance-dashboard?view=health
```

### Monitoring Dashboard
```bash
GET /api/admin/ai-performance-dashboard?view=overview
GET /api/admin/ai-performance-dashboard?view=performance
GET /api/admin/ai-performance-dashboard?view=cache
GET /api/admin/ai-performance-dashboard?view=requests
```

### Key Metrics
- **Response Time**: Average AI request processing time
- **Cache Hit Rate**: Percentage of requests served from cache
- **Throughput**: Requests processed per second
- **Error Rate**: Percentage of failed requests
- **Queue Length**: Number of pending requests

## 🔧 Configuration

### Cache Configuration
```typescript
const cacheConfig = {
  l1MaxSize: 1000,        // L1 cache max entries
  l1TTL: 300,             // L1 cache TTL (seconds)
  l2TTL: 3600,            // L2 cache TTL (seconds)
  compressionThreshold: 1024, // Compression threshold (bytes)
  warmupEnabled: true,    // Enable cache warming
  predictiveCaching: true, // Enable predictive caching
  backgroundRefresh: true  // Enable background refresh
};
```

### Request Optimizer Configuration
```typescript
const optimizerConfig = {
  batchSize: 5,                    // Max requests per batch
  batchTimeout: 100,               // Batch timeout (ms)
  maxConcurrentRequests: 10,       // Max concurrent requests
  deduplicationWindow: 5000,       // Deduplication window (ms)
  priorityLevels: 3,               // Number of priority levels
  compressionEnabled: true         // Enable response compression
};
```

### Performance Thresholds
```typescript
const thresholds = {
  responseTime: { warning: 2000, critical: 5000 },    // ms
  throughput: { warning: 10, critical: 5 },           // req/s
  errorRate: { warning: 0.05, critical: 0.1 },        // percentage
  cacheHitRate: { warning: 0.7, critical: 0.5 },      // percentage
  memoryUsage: { warning: 0.8, critical: 0.95 },      // percentage
  queueLength: { warning: 20, critical: 50 }          // count
};
```

## 🛡️ Security

### Input Validation
All inputs are automatically validated and sanitized:
- XSS prevention
- SQL injection protection
- Path traversal prevention
- Code injection detection
- Content length limits
- Character encoding validation

### Rate Limiting
- Per-user rate limiting (60 requests/minute default)
- Automatic cleanup of expired rate limits
- Intelligent scaling based on usage patterns

### Security Monitoring
- Real-time threat detection
- Security event logging
- Automatic blocking of high-risk content
- Compliance with security best practices

## 🔍 Troubleshooting

### Common Issues

#### High Response Times
1. Check cache hit rate
2. Monitor queue length
3. Review AI service connectivity
4. Check Redis connection status

#### Low Cache Hit Rate
1. Review cache TTL settings
2. Check cache warming configuration
3. Analyze access patterns
4. Consider increasing cache size

#### High Error Rate
1. Check AI service quota
2. Review input validation logs
3. Monitor network connectivity
4. Check authentication status

### Debug Logging
Enable debug logging for detailed insights:
```typescript
// Set log level to debug
process.env.LOG_LEVEL = 'debug';
```

### Performance Alerts
The system automatically generates alerts for:
- Response time degradation
- High error rates
- Cache performance issues
- Memory usage spikes
- Queue length increases

## 📈 Optimization Recommendations

### Cache Optimization
- Implement cache warming for frequently accessed data
- Optimize cache key generation for better hit rates
- Use compression for large cached objects
- Monitor and adjust TTL values based on usage patterns

### Request Optimization
- Enable request batching for similar operations
- Implement request prioritization
- Use deduplication to reduce redundant requests
- Optimize queue management for better throughput

### Performance Optimization
- Monitor response time trends
- Implement predictive scaling
- Use background refresh for cache updates
- Optimize database queries and connections

## 🔄 Maintenance

### Regular Tasks
- Monitor performance metrics daily
- Review error logs weekly
- Update cache configurations monthly
- Perform capacity planning quarterly

### Backup and Recovery
- Cache data is automatically replicated
- Request history is maintained for analysis
- Performance metrics are archived
- Configuration changes are logged

## 📚 API Reference

### Core Methods

#### `analyzeResume(resumeText: string, userId?: string)`
Analyzes resume content and provides comprehensive feedback.

**Parameters:**
- `resumeText`: The resume content to analyze
- `userId`: Optional user identifier for caching and rate limiting

**Returns:** `AIResponse` with analysis results

#### `generateCareerRecommendations(assessmentData, currentSkills, preferences, userId?)`
Generates personalized career recommendations.

**Parameters:**
- `assessmentData`: Career assessment results
- `currentSkills`: Array of current skills
- `preferences`: User preferences and constraints
- `userId`: Optional user identifier

**Returns:** `AIResponse` with career recommendations

#### `generateInterviewQuestions(params)`
Generates interview questions based on specified parameters.

**Parameters:**
- `sessionType`: Type of interview session
- `careerPath`: Target career path
- `experienceLevel`: Experience level
- `difficulty`: Question difficulty
- `count`: Number of questions to generate

**Returns:** `AIResponse` with generated questions

### Response Format
```typescript
interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
}
```

## 🎯 Best Practices

1. **Always handle errors gracefully**
2. **Use appropriate cache TTL values**
3. **Monitor performance metrics regularly**
4. **Implement proper input validation**
5. **Use rate limiting for user requests**
6. **Enable security monitoring**
7. **Optimize for your specific use case**
8. **Keep dependencies updated**
9. **Test thoroughly before deployment**
10. **Monitor and optimize continuously**

## 📞 Support

For technical support or questions:
- Check the troubleshooting section
- Review performance monitoring dashboard
- Enable debug logging for detailed insights
- Contact the development team with specific error details

---

*This documentation is automatically updated with each release. Last updated: 2025-06-19*
