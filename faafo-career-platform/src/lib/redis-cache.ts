/**
 * Redis-backed cache with memory fallback for AI service
 * Provides persistent caching that survives server restarts
 */

import Redis from 'ioredis';
import { MemoryCache } from './cache';

interface CacheInterface {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, data: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<boolean>;
  clear(): Promise<void>;
  has(key: string): Promise<boolean>;
}

class RedisCacheService implements CacheInterface {
  private client: Redis | null = null;
  private memoryFallback: MemoryCache;
  private isConnected = false;
  private connectionAttempts = 0;
  private maxConnectionAttempts = 3;
  private reconnectDelay = 5000; // 5 seconds

  constructor() {
    this.memoryFallback = new MemoryCache({ ttl: 5 * 60 * 1000, maxSize: 1000 });
    this.initializeRedis();
  }

  private async initializeRedis(): Promise<void> {
    try {
      const redisUrl = process.env.REDIS_URL;

      if (!redisUrl) {
        console.warn('[Redis-Cache] REDIS_URL not configured, using memory cache only');
        return;
      }

      this.client = new Redis(redisUrl, {
        connectTimeout: 5000,
        lazyConnect: true,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        retryDelayOnClusterDown: 300,
        enableOfflineQueue: false,
      });

      this.client.on('error', (err) => {
        console.error('[Redis-Cache] Redis client error:', err);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        console.log('[Redis-Cache] Redis client connected');
        this.isConnected = true;
        this.connectionAttempts = 0;
      });

      this.client.on('ready', () => {
        console.log('[Redis-Cache] Redis client ready');
        this.isConnected = true;
      });

      this.client.on('close', () => {
        console.log('[Redis-Cache] Redis client disconnected');
        this.isConnected = false;
      });

      await this.client.connect();

    } catch (error) {
      console.error('[Redis-Cache] Failed to initialize Redis:', error);
      this.connectionAttempts++;

      if (this.connectionAttempts < this.maxConnectionAttempts) {
        console.log(`[Redis-Cache] Retrying connection in ${this.reconnectDelay}ms (attempt ${this.connectionAttempts}/${this.maxConnectionAttempts})`);
        setTimeout(() => this.initializeRedis(), this.reconnectDelay);
      } else {
        console.warn('[Redis-Cache] Max connection attempts reached, using memory cache only');
      }
    }
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      if (this.isConnected && this.client) {
        const value = await this.client.get(key);
        if (value) {
          return JSON.parse(value) as T;
        }
      }
    } catch (error) {
      console.warn('[Redis-Cache] Redis get failed, falling back to memory:', error);
    }
    
    // Fallback to memory cache
    return this.memoryFallback.get<T>(key);
  }

  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    const serializedData = JSON.stringify(data);
    const ttlSeconds = ttl ? Math.floor(ttl / 1000) : undefined;

    try {
      if (this.isConnected && this.client) {
        if (ttlSeconds) {
          await this.client.setex(key, ttlSeconds, serializedData);
        } else {
          await this.client.set(key, serializedData);
        }
        return;
      }
    } catch (error) {
      console.warn('[Redis-Cache] Redis set failed, falling back to memory:', error);
    }

    // Fallback to memory cache
    this.memoryFallback.set(key, data, ttl);
  }

  async delete(key: string): Promise<boolean> {
    let redisResult = false;
    let memoryResult = false;

    try {
      if (this.isConnected && this.client) {
        const result = await this.client.del(key);
        redisResult = result > 0;
      }
    } catch (error) {
      console.warn('[Redis-Cache] Redis delete failed:', error);
    }
    
    // Also delete from memory cache
    memoryResult = this.memoryFallback.delete(key);
    
    return redisResult || memoryResult;
  }

  async clear(): Promise<void> {
    try {
      if (this.isConnected && this.client) {
        await this.client.flushdb();
      }
    } catch (error) {
      console.warn('[Redis-Cache] Redis clear failed:', error);
    }

    // Also clear memory cache
    this.memoryFallback.clear();
  }

  async has(key: string): Promise<boolean> {
    try {
      if (this.isConnected && this.client) {
        const exists = await this.client.exists(key);
        return exists === 1;
      }
    } catch (error) {
      console.warn('[Redis-Cache] Redis exists check failed, falling back to memory:', error);
    }
    
    // Fallback to memory cache
    return this.memoryFallback.has(key);
  }

  // Health check method
  async healthCheck(): Promise<{ redis: boolean; memory: boolean }> {
    let redisHealthy = false;
    
    try {
      if (this.isConnected && this.client) {
        await this.client.ping();
        redisHealthy = true;
      }
    } catch (error) {
      console.warn('[Redis-Cache] Health check failed:', error);
    }
    
    return {
      redis: redisHealthy,
      memory: true // Memory cache is always available
    };
  }

  // Get cache statistics
  getStats() {
    return {
      redis: {
        connected: this.isConnected,
        connectionAttempts: this.connectionAttempts
      },
      memory: this.memoryFallback.getStats()
    };
  }

  // Graceful shutdown
  async disconnect(): Promise<void> {
    try {
      if (this.client) {
        await this.client.disconnect();
      }
    } catch (error) {
      console.error('[Redis-Cache] Error during disconnect:', error);
    }
  }
}

// Create singleton instance
export const redisCache = new RedisCacheService();

// Export for use in AI service
export default redisCache;

// Graceful shutdown handling
if (typeof process !== 'undefined') {
  process.on('SIGTERM', async () => {
    console.log('[Redis-Cache] Received SIGTERM, closing Redis connection...');
    await redisCache.disconnect();
  });

  process.on('SIGINT', async () => {
    console.log('[Redis-Cache] Received SIGINT, closing Redis connection...');
    await redisCache.disconnect();
  });
}
