/**
 * Request Optimizer
 * Batching, deduplication, and intelligent request management
 */

import { AIServiceLogger } from './services/geminiService';

interface PendingRequest<T> {
  id: string;
  resolve: (value: T) => void;
  reject: (error: any) => void;
  timestamp: number;
  priority: number;
  userId?: string;
}

interface BatchRequest {
  requests: PendingRequest<any>[];
  batchKey: string;
  scheduledTime: number;
}

interface RequestStats {
  totalRequests: number;
  batchedRequests: number;
  deduplicatedRequests: number;
  averageBatchSize: number;
  averageWaitTime: number;
  throughputPerSecond: number;
}

interface OptimizerConfig {
  batchSize: number;
  batchTimeout: number;
  maxConcurrentRequests: number;
  deduplicationWindow: number;
  priorityLevels: number;
  compressionEnabled: boolean;
}

class RequestOptimizer {
  private pendingRequests: Map<string, PendingRequest<any>[]>;
  private batchQueue: Map<string, BatchRequest>;
  private activeRequests: Set<string>;
  private requestHistory: Map<string, number>;
  private stats: RequestStats;
  private config: OptimizerConfig;
  private requestCounter: number;

  constructor(config?: Partial<OptimizerConfig>) {
    this.pendingRequests = new Map();
    this.batchQueue = new Map();
    this.activeRequests = new Set();
    this.requestHistory = new Map();
    this.requestCounter = 0;

    this.config = {
      batchSize: 5,
      batchTimeout: 100, // 100ms
      maxConcurrentRequests: 10,
      deduplicationWindow: 5000, // 5 seconds
      priorityLevels: 3,
      compressionEnabled: true,
      ...config
    };

    this.stats = {
      totalRequests: 0,
      batchedRequests: 0,
      deduplicatedRequests: 0,
      averageBatchSize: 0,
      averageWaitTime: 0,
      throughputPerSecond: 0
    };

    this.startBackgroundProcesses();
    
    AIServiceLogger.info('Request Optimizer initialized', {
      config: this.config
    });
  }

  async optimizedRequest<T>(
    requestKey: string,
    requestFunction: () => Promise<T>,
    options: {
      priority?: number;
      userId?: string;
      cacheable?: boolean;
      timeout?: number;
    } = {}
  ): Promise<T> {
    const startTime = Date.now();
    const requestId = this.generateRequestId();
    
    this.stats.totalRequests++;

    try {
      // Check for deduplication
      const duplicateResult = await this.checkForDuplicate<T>(requestKey);
      if (duplicateResult !== null) {
        this.stats.deduplicatedRequests++;
        AIServiceLogger.debug('Request deduplicated', { 
          requestKey: this.truncateKey(requestKey),
          requestId 
        });
        return duplicateResult;
      }

      // Check if we should batch this request
      if (this.shouldBatch(requestKey)) {
        return await this.addToBatch<T>(requestKey, requestFunction, {
          id: requestId,
          priority: options.priority || 1,
          userId: options.userId,
          timestamp: startTime
        });
      }

      // Execute immediately if not batchable or under concurrency limit
      if (this.activeRequests.size < this.config.maxConcurrentRequests) {
        return await this.executeRequest<T>(requestKey, requestFunction, requestId);
      }

      // Queue the request if at concurrency limit
      return await this.queueRequest<T>(requestKey, requestFunction, {
        id: requestId,
        priority: options.priority || 1,
        userId: options.userId,
        timestamp: startTime
      });

    } catch (error) {
      AIServiceLogger.error('Optimized request failed', error, {
        requestKey: this.truncateKey(requestKey),
        requestId
      });
      throw error;
    } finally {
      this.updateStats(startTime);
    }
  }

  private async checkForDuplicate<T>(requestKey: string): Promise<T | null> {
    const now = Date.now();
    const lastRequestTime = this.requestHistory.get(requestKey);
    
    if (lastRequestTime && (now - lastRequestTime) < this.config.deduplicationWindow) {
      // Check if there's a pending request for the same key
      const pending = this.pendingRequests.get(requestKey);
      if (pending && pending.length > 0) {
        // Wait for the existing request to complete
        return new Promise((resolve, reject) => {
          pending.push({
            id: this.generateRequestId(),
            resolve,
            reject,
            timestamp: now,
            priority: 1
          });
        });
      }
    }

    return null;
  }

  private shouldBatch(requestKey: string): boolean {
    // Determine if this type of request should be batched
    const batchableTypes = [
      'resume-analysis',
      'skills-analysis',
      'career-recommendations'
    ];

    return batchableTypes.some(type => requestKey.includes(type));
  }

  private async addToBatch<T>(
    requestKey: string,
    requestFunction: () => Promise<T>,
    requestInfo: Omit<PendingRequest<T>, 'resolve' | 'reject'>
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const batchKey = this.getBatchKey(requestKey);
      const request: PendingRequest<T> = {
        ...requestInfo,
        resolve,
        reject
      };

      // Add to pending requests
      const pending = this.pendingRequests.get(requestKey) || [];
      pending.push(request);
      this.pendingRequests.set(requestKey, pending);

      // Add to batch queue
      let batch = this.batchQueue.get(batchKey);
      if (!batch) {
        batch = {
          requests: [],
          batchKey,
          scheduledTime: Date.now() + this.config.batchTimeout
        };
        this.batchQueue.set(batchKey, batch);
        
        // Schedule batch execution
        setTimeout(() => {
          this.executeBatch(batchKey);
        }, this.config.batchTimeout);
      }

      batch.requests.push(request);

      // Execute immediately if batch is full
      if (batch.requests.length >= this.config.batchSize) {
        this.executeBatch(batchKey);
      }

      AIServiceLogger.debug('Request added to batch', {
        batchKey,
        batchSize: batch.requests.length,
        requestId: request.id
      });
    });
  }

  private async executeBatch(batchKey: string): Promise<void> {
    const batch = this.batchQueue.get(batchKey);
    if (!batch || batch.requests.length === 0) return;

    this.batchQueue.delete(batchKey);
    this.stats.batchedRequests += batch.requests.length;

    try {
      AIServiceLogger.debug('Executing batch', {
        batchKey,
        requestCount: batch.requests.length
      });

      // Sort requests by priority
      batch.requests.sort((a, b) => b.priority - a.priority);

      // Execute requests in parallel with concurrency control
      const results = await this.executeBatchRequests(batch.requests);

      // Resolve all requests with their results
      batch.requests.forEach((request, index) => {
        if (results[index].success) {
          request.resolve(results[index].data);
        } else {
          request.reject(results[index].error);
        }
      });

    } catch (error) {
      // Reject all requests in the batch
      batch.requests.forEach(request => {
        request.reject(error);
      });
      
      AIServiceLogger.error('Batch execution failed', error, { batchKey });
    }
  }

  private async executeBatchRequests(requests: PendingRequest<any>[]): Promise<Array<{
    success: boolean;
    data?: any;
    error?: any;
  }>> {
    const results: Array<{ success: boolean; data?: any; error?: any }> = [];
    
    // Execute requests with controlled concurrency
    const concurrencyLimit = Math.min(this.config.maxConcurrentRequests, requests.length);
    const chunks = this.chunkArray(requests, concurrencyLimit);

    for (const chunk of chunks) {
      const chunkResults = await Promise.allSettled(
        chunk.map(async (request) => {
          // This would need to be implemented based on the actual request function
          // For now, we'll simulate the execution
          return { success: true, data: `Result for ${request.id}` };
        })
      );

      chunkResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({ success: false, error: result.reason });
        }
      });
    }

    return results;
  }

  private async executeRequest<T>(
    requestKey: string,
    requestFunction: () => Promise<T>,
    requestId: string
  ): Promise<T> {
    this.activeRequests.add(requestId);
    this.requestHistory.set(requestKey, Date.now());

    try {
      const result = await requestFunction();
      
      AIServiceLogger.debug('Request executed successfully', {
        requestKey: this.truncateKey(requestKey),
        requestId
      });
      
      return result;
    } finally {
      this.activeRequests.delete(requestId);
    }
  }

  private async queueRequest<T>(
    requestKey: string,
    requestFunction: () => Promise<T>,
    requestInfo: Omit<PendingRequest<T>, 'resolve' | 'reject'>
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const request: PendingRequest<T> = {
        ...requestInfo,
        resolve,
        reject
      };

      const pending = this.pendingRequests.get(requestKey) || [];
      pending.push(request);
      this.pendingRequests.set(requestKey, pending);

      AIServiceLogger.debug('Request queued', {
        requestKey: this.truncateKey(requestKey),
        queueSize: pending.length,
        requestId: request.id
      });
    });
  }

  private getBatchKey(requestKey: string): string {
    // Extract the operation type for batching
    const parts = requestKey.split(':');
    return parts[0] || 'default';
  }

  private generateRequestId(): string {
    return `req_${++this.requestCounter}_${Date.now()}`;
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  private startBackgroundProcesses(): void {
    // Process queued requests
    setInterval(() => {
      this.processQueuedRequests();
    }, 1000); // Every second

    // Update throughput stats
    setInterval(() => {
      this.updateThroughputStats();
    }, 10000); // Every 10 seconds

    // Cleanup old request history
    setInterval(() => {
      this.cleanupRequestHistory();
    }, 60000); // Every minute
  }

  private async processQueuedRequests(): Promise<void> {
    if (this.activeRequests.size >= this.config.maxConcurrentRequests) return;

    const availableSlots = this.config.maxConcurrentRequests - this.activeRequests.size;
    let processedCount = 0;

    for (const [requestKey, requests] of this.pendingRequests.entries()) {
      if (processedCount >= availableSlots) break;
      if (requests.length === 0) continue;

      // Get highest priority request
      requests.sort((a, b) => b.priority - a.priority);
      const request = requests.shift()!;

      if (requests.length === 0) {
        this.pendingRequests.delete(requestKey);
      }

      // Execute the request
      try {
        // This would need actual implementation based on the request type
        const result = `Processed result for ${request.id}`;
        request.resolve(result);
        processedCount++;
      } catch (error) {
        request.reject(error);
      }
    }
  }

  private updateThroughputStats(): void {
    const now = Date.now();
    const windowStart = now - 10000; // 10 second window
    
    // This would be enhanced with actual request tracking
    this.stats.throughputPerSecond = this.stats.totalRequests / 10;
    
    AIServiceLogger.debug('Throughput stats updated', {
      throughput: this.stats.throughputPerSecond,
      activeRequests: this.activeRequests.size,
      queuedRequests: Array.from(this.pendingRequests.values()).reduce((sum, arr) => sum + arr.length, 0)
    });
  }

  private cleanupRequestHistory(): void {
    const now = Date.now();
    const cutoff = now - this.config.deduplicationWindow;
    
    for (const [key, timestamp] of this.requestHistory.entries()) {
      if (timestamp < cutoff) {
        this.requestHistory.delete(key);
      }
    }
  }

  private updateStats(startTime: number): void {
    const waitTime = Date.now() - startTime;
    this.stats.averageWaitTime = 
      (this.stats.averageWaitTime * (this.stats.totalRequests - 1) + waitTime) / this.stats.totalRequests;
  }

  private truncateKey(key: string): string {
    return key.length > 50 ? key.substring(0, 50) + '...' : key;
  }

  // Public API
  getStats(): RequestStats {
    return { ...this.stats };
  }

  getConfig(): OptimizerConfig {
    return { ...this.config };
  }

  getActiveRequestCount(): number {
    return this.activeRequests.size;
  }

  getQueuedRequestCount(): number {
    return Array.from(this.pendingRequests.values()).reduce((sum, arr) => sum + arr.length, 0);
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    activeRequests: number;
    queuedRequests: number;
    throughput: number;
    averageWaitTime: number;
  }> {
    const queuedCount = this.getQueuedRequestCount();
    const status = queuedCount > 50 ? 'unhealthy' : 
                  queuedCount > 20 ? 'degraded' : 'healthy';

    return {
      status,
      activeRequests: this.activeRequests.size,
      queuedRequests: queuedCount,
      throughput: this.stats.throughputPerSecond,
      averageWaitTime: this.stats.averageWaitTime
    };
  }
}

// Export singleton instance
export const requestOptimizer = new RequestOptimizer();
export { RequestOptimizer };
