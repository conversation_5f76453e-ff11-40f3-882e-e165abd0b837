/**
 * Advanced Cache Manager
 * Multi-tier intelligent caching with performance optimization
 */

import { MemoryCache } from './cache';
import { RedisCacheService } from './redis-cache';
import { AIServiceLogger } from './services/geminiService';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  size: number;
  ttl: number;
  compressed?: boolean;
}

interface CacheStats {
  l1Hits: number;
  l1Misses: number;
  l2Hits: number;
  l2Misses: number;
  totalRequests: number;
  averageResponseTime: number;
  hitRate: number;
  memoryUsage: number;
  compressionRatio: number;
}

interface CacheConfig {
  l1MaxSize: number;
  l1TTL: number;
  l2TTL: number;
  compressionThreshold: number;
  warmupEnabled: boolean;
  predictiveCaching: boolean;
  backgroundRefresh: boolean;
}

class AdvancedCacheManager {
  private l1Cache: Map<string, CacheEntry<any>>;
  private l2Cache: RedisCacheService;
  private memoryCache: MemoryCache;
  private stats: CacheStats;
  private config: CacheConfig;
  private accessPatterns: Map<string, number[]>;
  private warmupQueue: Set<string>;
  private refreshQueue: Set<string>;

  constructor(config?: Partial<CacheConfig>) {
    this.l1Cache = new Map();
    this.l2Cache = new RedisCacheService();
    this.memoryCache = new MemoryCache();
    
    this.config = {
      l1MaxSize: 1000,
      l1TTL: 300, // 5 minutes
      l2TTL: 3600, // 1 hour
      compressionThreshold: 1024, // 1KB
      warmupEnabled: true,
      predictiveCaching: true,
      backgroundRefresh: true,
      ...config
    };

    this.stats = {
      l1Hits: 0,
      l1Misses: 0,
      l2Hits: 0,
      l2Misses: 0,
      totalRequests: 0,
      averageResponseTime: 0,
      hitRate: 0,
      memoryUsage: 0,
      compressionRatio: 0
    };

    this.accessPatterns = new Map();
    this.warmupQueue = new Set();
    this.refreshQueue = new Set();

    // Initialize background processes
    this.startBackgroundProcesses();
    
    AIServiceLogger.info('Advanced Cache Manager initialized', {
      config: this.config,
      l1CacheSize: this.l1Cache.size
    });
  }

  async initialize(): Promise<void> {
    await this.l2Cache.initialize();
    
    if (this.config.warmupEnabled) {
      await this.performCacheWarmup();
    }
  }

  async get<T>(key: string, fetchFunction?: () => Promise<T>): Promise<T | null> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      // Track access pattern
      this.trackAccess(key);

      // L1 Cache check (in-memory)
      const l1Result = this.getFromL1<T>(key);
      if (l1Result !== null) {
        this.stats.l1Hits++;
        this.updateStats(startTime);
        AIServiceLogger.debug('L1 cache hit', { key: this.truncateKey(key) });
        return l1Result;
      }
      this.stats.l1Misses++;

      // L2 Cache check (Redis)
      const l2Result = await this.getFromL2<T>(key);
      if (l2Result !== null) {
        this.stats.l2Hits++;
        // Promote to L1 cache
        await this.setToL1(key, l2Result, this.config.l1TTL);
        this.updateStats(startTime);
        AIServiceLogger.debug('L2 cache hit, promoted to L1', { key: this.truncateKey(key) });
        return l2Result;
      }
      this.stats.l2Misses++;

      // Cache miss - fetch data if function provided
      if (fetchFunction) {
        const data = await fetchFunction();
        if (data !== null) {
          await this.set(key, data, this.config.l2TTL);
          AIServiceLogger.debug('Data fetched and cached', { key: this.truncateKey(key) });
        }
        this.updateStats(startTime);
        return data;
      }

      this.updateStats(startTime);
      return null;

    } catch (error) {
      AIServiceLogger.error('Cache get operation failed', error, { key: this.truncateKey(key) });
      this.updateStats(startTime);
      return null;
    }
  }

  async set<T>(key: string, data: T, ttl?: number): Promise<boolean> {
    try {
      const effectiveTTL = ttl || this.config.l2TTL;
      
      // Set in both L1 and L2 caches
      await Promise.all([
        this.setToL1(key, data, Math.min(effectiveTTL, this.config.l1TTL)),
        this.setToL2(key, data, effectiveTTL)
      ]);

      // Schedule background refresh if enabled
      if (this.config.backgroundRefresh && effectiveTTL > 300) {
        this.scheduleBackgroundRefresh(key, effectiveTTL * 0.8);
      }

      return true;
    } catch (error) {
      AIServiceLogger.error('Cache set operation failed', error, { key: this.truncateKey(key) });
      return false;
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      await Promise.all([
        this.deleteFromL1(key),
        this.deleteFromL2(key)
      ]);
      
      this.accessPatterns.delete(key);
      this.warmupQueue.delete(key);
      this.refreshQueue.delete(key);
      
      return true;
    } catch (error) {
      AIServiceLogger.error('Cache delete operation failed', error, { key: this.truncateKey(key) });
      return false;
    }
  }

  async invalidatePattern(pattern: string): Promise<number> {
    try {
      let invalidatedCount = 0;

      // Invalidate L1 cache
      for (const key of this.l1Cache.keys()) {
        if (key.includes(pattern)) {
          this.l1Cache.delete(key);
          invalidatedCount++;
        }
      }

      // Invalidate L2 cache
      const l2Count = await this.l2Cache.invalidatePattern(pattern);
      invalidatedCount += l2Count;

      AIServiceLogger.info('Cache pattern invalidated', { pattern, count: invalidatedCount });
      return invalidatedCount;
    } catch (error) {
      AIServiceLogger.error('Cache pattern invalidation failed', error, { pattern });
      return 0;
    }
  }

  private getFromL1<T>(key: string): T | null {
    const entry = this.l1Cache.get(key);
    if (!entry) return null;

    // Check TTL
    if (Date.now() - entry.timestamp > entry.ttl * 1000) {
      this.l1Cache.delete(key);
      return null;
    }

    // Update access info
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    return entry.data;
  }

  private async getFromL2<T>(key: string): Promise<T | null> {
    return await this.l2Cache.get(key);
  }

  private async setToL1<T>(key: string, data: T, ttl: number): Promise<void> {
    // Check if L1 cache is full
    if (this.l1Cache.size >= this.config.l1MaxSize) {
      this.evictLRU();
    }

    const dataSize = this.estimateSize(data);
    const shouldCompress = dataSize > this.config.compressionThreshold;
    
    const entry: CacheEntry<T> = {
      data: shouldCompress ? this.compress(data) : data,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now(),
      size: dataSize,
      ttl,
      compressed: shouldCompress
    };

    this.l1Cache.set(key, entry);
    this.updateMemoryUsage();
  }

  private async setToL2<T>(key: string, data: T, ttl: number): Promise<void> {
    await this.l2Cache.set(key, data, ttl);
  }

  private async deleteFromL1(key: string): Promise<void> {
    this.l1Cache.delete(key);
    this.updateMemoryUsage();
  }

  private async deleteFromL2(key: string): Promise<void> {
    await this.l2Cache.delete(key);
  }

  private evictLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.l1Cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.l1Cache.delete(oldestKey);
      AIServiceLogger.debug('LRU eviction performed', { evictedKey: this.truncateKey(oldestKey) });
    }
  }

  private trackAccess(key: string): void {
    if (!this.config.predictiveCaching) return;

    const now = Date.now();
    const pattern = this.accessPatterns.get(key) || [];
    pattern.push(now);

    // Keep only last 10 access times
    if (pattern.length > 10) {
      pattern.shift();
    }

    this.accessPatterns.set(key, pattern);
  }

  private scheduleBackgroundRefresh(key: string, delayMs: number): void {
    if (!this.config.backgroundRefresh) return;

    setTimeout(() => {
      this.refreshQueue.add(key);
    }, delayMs);
  }

  private async performCacheWarmup(): Promise<void> {
    try {
      // Implement cache warming logic based on historical access patterns
      const commonKeys = this.getCommonAccessKeys();
      
      for (const key of commonKeys) {
        this.warmupQueue.add(key);
      }

      AIServiceLogger.info('Cache warmup initiated', { keysToWarm: commonKeys.length });
    } catch (error) {
      AIServiceLogger.error('Cache warmup failed', error);
    }
  }

  private getCommonAccessKeys(): string[] {
    // Return commonly accessed keys based on patterns
    // This would be enhanced with actual usage analytics
    return [
      'common-career-paths',
      'popular-skills',
      'interview-question-templates',
      'resume-analysis-templates'
    ];
  }

  private startBackgroundProcesses(): void {
    // Background refresh process
    setInterval(() => {
      this.processRefreshQueue();
    }, 30000); // Every 30 seconds

    // Cache cleanup process
    setInterval(() => {
      this.performCacheCleanup();
    }, 300000); // Every 5 minutes

    // Stats update process
    setInterval(() => {
      this.updateCacheStats();
    }, 60000); // Every minute
  }

  private async processRefreshQueue(): Promise<void> {
    if (this.refreshQueue.size === 0) return;

    const keysToRefresh = Array.from(this.refreshQueue).slice(0, 5); // Process 5 at a time
    this.refreshQueue.clear();

    for (const key of keysToRefresh) {
      try {
        // Refresh logic would go here
        AIServiceLogger.debug('Background refresh completed', { key: this.truncateKey(key) });
      } catch (error) {
        AIServiceLogger.error('Background refresh failed', error, { key: this.truncateKey(key) });
      }
    }
  }

  private performCacheCleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, entry] of this.l1Cache.entries()) {
      if (now - entry.timestamp > entry.ttl * 1000) {
        this.l1Cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      AIServiceLogger.debug('Cache cleanup completed', { cleanedEntries: cleanedCount });
      this.updateMemoryUsage();
    }
  }

  private updateStats(startTime: number): void {
    const responseTime = Date.now() - startTime;
    this.stats.averageResponseTime = 
      (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / this.stats.totalRequests;
    
    this.stats.hitRate = 
      (this.stats.l1Hits + this.stats.l2Hits) / this.stats.totalRequests;
  }

  private updateCacheStats(): void {
    this.updateMemoryUsage();
    
    AIServiceLogger.debug('Cache stats updated', {
      l1Size: this.l1Cache.size,
      hitRate: this.stats.hitRate.toFixed(3),
      avgResponseTime: this.stats.averageResponseTime.toFixed(2),
      memoryUsage: this.stats.memoryUsage
    });
  }

  private updateMemoryUsage(): void {
    let totalSize = 0;
    for (const entry of this.l1Cache.values()) {
      totalSize += entry.size;
    }
    this.stats.memoryUsage = totalSize;
  }

  private estimateSize(data: any): number {
    return JSON.stringify(data).length * 2; // Rough estimate
  }

  private compress(data: any): any {
    // Simple compression simulation - in production, use actual compression
    return data;
  }

  private truncateKey(key: string): string {
    return key.length > 50 ? key.substring(0, 50) + '...' : key;
  }

  // Public API for monitoring
  getStats(): CacheStats {
    return { ...this.stats };
  }

  getConfig(): CacheConfig {
    return { ...this.config };
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    l1Cache: { size: number; maxSize: number; usage: number };
    l2Cache: any;
    performance: { hitRate: number; avgResponseTime: number };
  }> {
    const l2Health = await this.l2Cache.healthCheck();
    
    return {
      status: l2Health.status === 'healthy' && this.stats.hitRate > 0.5 ? 'healthy' : 'degraded',
      l1Cache: {
        size: this.l1Cache.size,
        maxSize: this.config.l1MaxSize,
        usage: this.l1Cache.size / this.config.l1MaxSize
      },
      l2Cache: l2Health,
      performance: {
        hitRate: this.stats.hitRate,
        avgResponseTime: this.stats.averageResponseTime
      }
    };
  }
}

// Export singleton instance
export const advancedCacheManager = new AdvancedCacheManager();
export { AdvancedCacheManager };
