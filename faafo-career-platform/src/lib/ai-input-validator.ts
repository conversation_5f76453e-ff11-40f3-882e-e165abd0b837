/**
 * Comprehensive input validation and security hardening for AI service
 * Protects against malicious inputs, injection attacks, and data integrity issues
 */

import DOMPurify from 'dompurify';
import { JSD<PERSON> } from 'jsdom';

// Create DOMPurify instance for server-side use
const window = new JSDOM('').window;
const purify = DOMPurify(window as any);

interface ValidationResult {
  isValid: boolean;
  sanitizedInput?: string;
  errors: string[];
  warnings: string[];
}

interface ValidationOptions {
  maxLength?: number;
  minLength?: number;
  allowHtml?: boolean;
  allowSpecialChars?: boolean;
  requireAlphanumeric?: boolean;
  customPatterns?: RegExp[];
}

export class AIInputValidator {
  // Dangerous patterns that should be blocked
  private static readonly DANGEROUS_PATTERNS = [
    // Script injection
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /data:text\/html/gi,
    
    // Event handlers
    /on\w+\s*=/gi,
    
    // SQL injection patterns
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
    
    // Command injection
    /(\||&|;|\$\(|\`)/g,
    
    // Path traversal
    /\.\.\//g,
    /(\.\.\\)/g,
    
    // XSS patterns
    /<iframe/gi,
    /<object/gi,
    /<embed/gi,
    /<link/gi,
    /<meta/gi,
    
    // Suspicious protocols
    /file:/gi,
    /ftp:/gi,
    /ldap:/gi,
  ];

  // Suspicious content patterns
  private static readonly SUSPICIOUS_PATTERNS = [
    // Excessive repetition
    /(.)\1{50,}/g,
    
    // Excessive special characters
    /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{20,}/g,
    
    // Potential prompt injection
    /ignore\s+(previous|all)\s+(instructions|prompts)/gi,
    /system\s*:\s*/gi,
    /assistant\s*:\s*/gi,
    /human\s*:\s*/gi,
    
    // Potential data extraction attempts
    /show\s+me\s+(your|the)\s+(system|internal|hidden)/gi,
    /what\s+(are\s+)?your\s+(instructions|prompts|rules)/gi,
  ];

  /**
   * Validate and sanitize text input for AI processing
   */
  static validateTextInput(
    input: string,
    options: ValidationOptions = {}
  ): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Set default options
    const opts = {
      maxLength: 10000,
      minLength: 1,
      allowHtml: false,
      allowSpecialChars: true,
      requireAlphanumeric: false,
      ...options
    };

    // Basic validation
    if (!input || typeof input !== 'string') {
      errors.push('Input must be a non-empty string');
      return { isValid: false, errors, warnings };
    }

    // Length validation
    if (input.length < opts.minLength) {
      errors.push(`Input must be at least ${opts.minLength} characters long`);
    }

    if (input.length > opts.maxLength) {
      errors.push(`Input must not exceed ${opts.maxLength} characters`);
    }

    // Check for dangerous patterns
    for (const pattern of this.DANGEROUS_PATTERNS) {
      if (pattern.test(input)) {
        errors.push('Input contains potentially dangerous content');
        break;
      }
    }

    // Check for suspicious patterns
    for (const pattern of this.SUSPICIOUS_PATTERNS) {
      if (pattern.test(input)) {
        warnings.push('Input contains suspicious patterns');
        break;
      }
    }

    // Custom pattern validation
    if (opts.customPatterns) {
      for (const pattern of opts.customPatterns) {
        if (!pattern.test(input)) {
          errors.push('Input does not match required pattern');
        }
      }
    }

    // Alphanumeric requirement
    if (opts.requireAlphanumeric && !/[a-zA-Z0-9]/.test(input)) {
      errors.push('Input must contain at least one alphanumeric character');
    }

    // Sanitize input
    let sanitizedInput = input;

    // Remove null bytes and control characters
    sanitizedInput = sanitizedInput.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

    // HTML sanitization if not allowed
    if (!opts.allowHtml) {
      sanitizedInput = purify.sanitize(sanitizedInput, { 
        ALLOWED_TAGS: [],
        ALLOWED_ATTR: []
      });
    } else {
      // Basic HTML sanitization
      sanitizedInput = purify.sanitize(sanitizedInput);
    }

    // Normalize whitespace
    sanitizedInput = sanitizedInput.replace(/\s+/g, ' ').trim();

    // Special character handling
    if (!opts.allowSpecialChars) {
      sanitizedInput = sanitizedInput.replace(/[^\w\s\-.,!?]/g, '');
    }

    return {
      isValid: errors.length === 0,
      sanitizedInput,
      errors,
      warnings
    };
  }

  /**
   * Validate interview question parameters
   */
  static validateInterviewParams(params: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate session type
    const validSessionTypes = ['TECHNICAL_PRACTICE', 'BEHAVIORAL_PRACTICE', 'MOCK_INTERVIEW', 'QUICK_PRACTICE'];
    if (params.sessionType && !validSessionTypes.includes(params.sessionType)) {
      errors.push('Invalid session type');
    }

    // Validate experience level
    const validExperienceLevels = ['ENTRY', 'JUNIOR', 'INTERMEDIATE', 'SENIOR', 'EXECUTIVE'];
    if (params.experienceLevel && !validExperienceLevels.includes(params.experienceLevel)) {
      errors.push('Invalid experience level');
    }

    // Validate difficulty
    const validDifficulties = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
    if (params.difficulty && !validDifficulties.includes(params.difficulty)) {
      errors.push('Invalid difficulty level');
    }

    // Validate count
    if (params.count) {
      const count = parseInt(params.count);
      if (isNaN(count) || count < 1 || count > 50) {
        errors.push('Question count must be between 1 and 50');
      }
    }

    // Validate string parameters
    const stringParams = ['careerPath', 'companyType', 'industryFocus', 'specificRole', 'interviewType'];
    for (const param of stringParams) {
      if (params[param]) {
        const validation = this.validateTextInput(params[param], {
          maxLength: 200,
          allowHtml: false,
          allowSpecialChars: true
        });
        if (!validation.isValid) {
          errors.push(`Invalid ${param}: ${validation.errors.join(', ')}`);
        }
      }
    }

    // Validate focus areas array
    if (params.focusAreas && Array.isArray(params.focusAreas)) {
      if (params.focusAreas.length > 10) {
        errors.push('Too many focus areas (maximum 10)');
      }
      for (const area of params.focusAreas) {
        if (typeof area !== 'string' || area.length > 100) {
          errors.push('Invalid focus area');
          break;
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate interview response text
   */
  static validateInterviewResponse(responseText: string): ValidationResult {
    return this.validateTextInput(responseText, {
      maxLength: 5000,
      minLength: 10,
      allowHtml: false,
      allowSpecialChars: true,
      requireAlphanumeric: true
    });
  }

  /**
   * Validate resume text
   */
  static validateResumeText(resumeText: string): ValidationResult {
    return this.validateTextInput(resumeText, {
      maxLength: 20000,
      minLength: 100,
      allowHtml: false,
      allowSpecialChars: true,
      requireAlphanumeric: true
    });
  }

  /**
   * Validate career skills array
   */
  static validateSkillsArray(skills: string[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!Array.isArray(skills)) {
      errors.push('Skills must be provided as an array');
      return { isValid: false, errors, warnings };
    }

    if (skills.length === 0) {
      errors.push('At least one skill must be provided');
    }

    if (skills.length > 50) {
      errors.push('Too many skills provided (maximum 50)');
    }

    const validatedSkills: string[] = [];
    for (const skill of skills) {
      const validation = this.validateTextInput(skill, {
        maxLength: 100,
        minLength: 2,
        allowHtml: false,
        allowSpecialChars: false,
        requireAlphanumeric: true
      });

      if (validation.isValid && validation.sanitizedInput) {
        validatedSkills.push(validation.sanitizedInput);
      } else {
        warnings.push(`Invalid skill ignored: ${skill}`);
      }
    }

    return {
      isValid: errors.length === 0 && validatedSkills.length > 0,
      sanitizedInput: validatedSkills.join(','), // Return as comma-separated string
      errors,
      warnings
    };
  }

  /**
   * Rate limiting validation
   */
  static validateRateLimit(userId: string, requestType: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate user ID format
    if (!userId || typeof userId !== 'string') {
      errors.push('Invalid user ID');
    } else if (userId.length > 100) {
      errors.push('User ID too long');
    }

    // Validate request type
    const validRequestTypes = ['question_generation', 'response_analysis', 'resume_analysis', 'career_recommendations'];
    if (!validRequestTypes.includes(requestType)) {
      errors.push('Invalid request type');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Comprehensive security scan
   */
  static securityScan(input: string): { threats: string[]; riskLevel: 'low' | 'medium' | 'high' } {
    const threats: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    // Check for high-risk patterns
    for (const pattern of this.DANGEROUS_PATTERNS) {
      if (pattern.test(input)) {
        threats.push('Potential code injection detected');
        riskLevel = 'high';
        break;
      }
    }

    // Check for medium-risk patterns
    if (riskLevel !== 'high') {
      for (const pattern of this.SUSPICIOUS_PATTERNS) {
        if (pattern.test(input)) {
          threats.push('Suspicious content pattern detected');
          riskLevel = 'medium';
          break;
        }
      }
    }

    // Check for excessive length
    if (input.length > 50000) {
      threats.push('Excessive input length');
      riskLevel = riskLevel === 'low' ? 'medium' : riskLevel;
    }

    return { threats, riskLevel };
  }
}

export default AIInputValidator;
